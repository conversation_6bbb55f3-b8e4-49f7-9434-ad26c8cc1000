{"name": "@nextssp/source", "version": "0.0.1", "private": true, "license": "Private", "scripts": {}, "devDependencies": {"@eslint/eslintrc": "^2.1.1", "@eslint/js": "^9.8.0", "@nuxt/devtools": "1.0.0", "@nuxt/eslint-config": "~0.5.6", "@nuxt/kit": "^3.10.0", "@nuxt/ui-templates": "^1.3.1", "@nx/cypress": "21.3.11", "@nx/eslint": "21.3.11", "@nx/eslint-plugin": "21.3.11", "@nx/js": "21.3.11", "@nx/nuxt": "21.3.11", "@nx/vite": "21.3.11", "@nx/vue": "21.3.11", "@nx/web": "21.3.11", "@nx/workspace": "21.3.11", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/node": "18.16.9", "@typescript-eslint/parser": "^7.16.0", "@vitejs/plugin-vue": "^5.2.3", "@vitest/coverage-v8": "^3.0.5", "@vitest/ui": "^3.0.0", "@vue/eslint-config-prettier": "7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/test-utils": "^2.4.6", "cypress": "^14.2.1", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-cypress": "^3.5.0", "eslint-plugin-vue": "^9.16.1", "h3": "^1.8.2", "jiti": "2.4.2", "jsdom": "~22.1.0", "nuxt": "^3.10.0", "nx": "21.3.11", "prettier": "^2.6.2", "sass": "1.62.1", "tslib": "^2.3.0", "typescript": "~5.8.2", "typescript-eslint": "^8.29.0", "vite": "^6.0.0", "vitest": "^3.0.0", "vue-tsc": "^2.2.8"}, "dependencies": {"vue": "^3.5.13", "vue-router": "^4.5.0"}}